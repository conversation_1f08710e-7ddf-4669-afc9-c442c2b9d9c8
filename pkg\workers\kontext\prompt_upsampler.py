"""
[二次开发] Kontext 提示词增强器

负责根据用户意图优化生成参数，包括 guidance、steps 等。

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Kontext工作流的参数优化和增强
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-04 业务逻辑实现
- 依赖关系：无外部依赖
"""

import logging
from typing import Dict, Any

class PromptUpsampler:
    """
    Kontext 提示词增强器
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def optimize_generation_params(self, user_intent: str) -> Dict[str, Any]:
        """根据用户意图优化生成参数"""
        
        # 基础参数（Kontext专用：guidance范围2.0-3.0，官方标准2.5）
        params = {
            "guidance": 2.3,  # Kontext默认guidance值（略低于官方标准2.5）
            "steps": 20,      # 标准步数
            "prompt_upsampling": False
        }
        
        user_intent_lower = user_intent.lower()
        
        # 变换复杂度检测
        complex_transformations = [
            "cyborg", "机器人", "赛博朋克", "cyberpunk", "风格", "style", 
            "变成", "改为", "transform", "convert", "背景", "background",
            "换装", "古风", "现代", "未来", "科幻", "fantasy", "古代"
        ]
        
        style_changes = [
            "油画", "水彩", "素描", "动漫", "anime", "卡通", "painting", 
            "sketch", "watercolor", "art style", "artistic"
        ]
        
        major_changes = [
            "完全", "彻底", "dramatically", "completely", "entirely",
            "大幅", "大量", "massive", "major"
        ]
        
        # 复杂变换需要更强的引导（Kontext专用：2.0-3.0范围）
        if any(keyword in user_intent_lower for keyword in complex_transformations):
            params["guidance"] = 2.8  # 降低到2.8（原3.2）
            params["steps"] = 70

        # 风格变换需要中等引导但更多步数
        if any(keyword in user_intent_lower for keyword in style_changes):
            params["guidance"] = 2.6  # 降低到2.6（原3.0）
            params["steps"] = 65

        # 重大变换需要最强引导（Kontext最高3.0，降低上限）
        if any(keyword in user_intent_lower for keyword in major_changes):
            params["guidance"] = 3.0  # 降低到3.0（原3.5）
            params["steps"] = 80
        
        # 细节相关调整（限制在2.0-3.0范围内）
        if any(keyword in user_intent_lower for keyword in ["高质量", "精细", "详细", "high quality", "detailed", "精确"]):
            params["steps"] = min(params["steps"] + 20, 100)
            params["guidance"] = min(params["guidance"] + 0.4, 3.0)  # 最高3.0（降低）
        elif any(keyword in user_intent_lower for keyword in ["快速", "简单", "fast", "quick", "简约"]):
            params["steps"] = max(params["steps"] - 20, 30)
            params["guidance"] = max(params["guidance"] - 0.3, 2.0)  # 最低2.0

        # 细微调整（保持原有特征）
        if any(keyword in user_intent_lower for keyword in ["微调", "轻微", "subtle", "slight", "保持", "maintain"]):
            params["guidance"] = max(params["guidance"] - 0.3, 2.0)  # 最低2.0，减少幅度
        
        self.logger.info(f"根据用户意图优化参数: guidance={params['guidance']}, steps={params['steps']}")
        return params 